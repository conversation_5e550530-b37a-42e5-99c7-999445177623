import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Car<PERSON>ianG<PERSON>,
  Legend,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

interface OperationalMetricsChartProps {
  data: {
    operationalMetrics: Array<{
      unit: string
      waitTime: number
      noShowRate: number
      roomTurnover: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const OperationalMetricsChart: React.FC<OperationalMetricsChartProps> = ({
  data,
}) => {
  // Industry benchmarks
  const benchmarks = {
    waitTime: 15, // minutes
    noShowRate: 7, // percent
    roomTurnover: 20, // minutes
  }

  // Transform data for grouped bar chart
  const transformedData = data.operationalMetrics.map((item) => ({
    unit: item.unit,
    "Wait Time (min)": item.waitTime,
    "No-Show Rate (%)": item.noShowRate,
    "Room Turnover (min)": item.roomTurnover,
  }))

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <BarChart data={transformedData} barGap={0} barCategoryGap="20%">
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="unit" tick={{ fontSize: 12 }} />
        <YAxis
          label={{
            value: "Minutes / Percent",
            angle: -90,
            position: "insideLeft",
            fontSize: 12,
          }}
          tick={{ fontSize: 12 }}
        />
        <Tooltip
          formatter={(value, name) => {
            if (name === "Wait Time (min)") return [`${value} min`, name]
            if (name === "No-Show Rate (%)") return [`${value}%`, name]
            if (name === "Room Turnover (min)") return [`${value} min`, name]
            return [value, name]
          }}
        />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
        />
        <Bar dataKey="Wait Time (min)" fill="#8884d8" name="Wait Time (min)" />
        <Bar
          dataKey="No-Show Rate (%)"
          fill="#82ca9d"
          name="No-Show Rate (%)"
        />
        <Bar
          dataKey="Room Turnover (min)"
          fill="#ffc658"
          name="Room Turnover (min)"
        />
        <ReferenceLine
          y={benchmarks.waitTime}
          stroke="#000"
          strokeDasharray="3 3"
        />
        <ReferenceLine
          y={benchmarks.noShowRate}
          stroke="#000"
          strokeDasharray="3 3"
        />
        <ReferenceLine
          y={benchmarks.roomTurnover}
          stroke="#000"
          strokeDasharray="3 3"
        />
      </BarChart>
    </ResponsiveContainer>
  )
}

export default OperationalMetricsChart
